[Settings]
LogLevel=Info
AutoApprove=0
RandomSeatAssignment=1

[BiometricDevices]
FingerprintScanner=Simulated
SignaturePad=Simulated

[Camera]
CameraName=HD Pro Webcam C920
CameraIndex=0

[Verification]
SignatureVerification=0
PhotoVerificationMode=Both
SignatureVerificationMode=Both
FingerprintVerificationMode=Both
EnablePostExamVerification=0
RightThumbprintVerification=1

; Confidence thresholds (0-100)
; Skip manual verification if confidence is above these values
PhotoConfidenceThreshold=85
SignatureConfidenceThreshold=85
FingerprintConfidenceThreshold=85
; Fingerprint capture quality threshold
FingerprintCaptureThreshold=70
; Auto-save fingerprint when quality is above this threshold (0-100)
; In save mode: Automatically marks as "Saved" without requiring verification
; In compare mode: Marks as "Ready (High Quality)" and uses a slightly lower security level for matching
FingerprintAutoSaveThreshold=80
; Replaced AllowMissingFingerprint with FingerprintMode
; Options: save (store new fingerprint), compare (verify against existing)
FingerprintMode=save
Fingerprint2Mode=save
; Enable/disable right thumbprint verification (0=disabled, 1=enabled)
RightThumbprintVerification=1

[Paths]
; Database path - set this to customize the location of your database folder
dbPath=db
BinPath=bin
DatabasePath=db
ExportPath=export
ImagesPath=img
ImportPath=import
LogsPath=logs
TempPath=temp

[Network]
EnableNetworkSharing=0
SharedDatabasePath=
RefreshInterval=300
