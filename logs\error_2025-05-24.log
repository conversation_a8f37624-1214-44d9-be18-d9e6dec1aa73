=== WinCBT-Biometric Error Log ===
Started: 2025-05-24 11:06:31
----------------------------------------
2025-05-24 11:06:31 [INFO] Error handler initialized
2025-05-24 11:06:32 [INFO] Read database path from config: db
2025-05-24 11:06:32 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:06:32 [INFO] Error handler initialized
2025-05-24 11:06:32 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:06:32 [INFO] PathManager initialized successfully
2025-05-24 11:06:32 [INFO] PathManager initialized successfully
2025-05-24 11:06:32 [INFO] Validating required files and directories
2025-05-24 11:06:32 [INFO] Validated directory: Database
2025-05-24 11:06:32 [INFO] Validated directory: Images
2025-05-24 11:06:32 [INFO] Validated directory: Logs
2025-05-24 11:06:32 [INFO] Validated directory: Temporary files
2025-05-24 11:06:32 [INFO] Validated directory: Candidate images
2025-05-24 11:06:32 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:06:32 [INFO] All required files and directories validated successfully
2025-05-24 11:06:32 [INFO] Initializing application components
2025-05-24 11:06:32 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:06:32 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:06:32 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:06:32 [INFO] Room cache loaded with 3 entries
2025-05-24 11:06:32 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:06:32 [INFO] Loaded 1 seat assignments
2025-05-24 11:06:32 [INFO] Database manager initialized successfully
2025-05-24 11:06:32 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:06:34 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:06:34 [DEBUG] IsObject check after creation: True
2025-05-24 11:06:34 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:06:34 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:06:34 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:06:34 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:06:34 [DEBUG] g_fingerprintManager class handle: 10397840
2025-05-24 11:06:35 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:06:36 [INFO] Webcam started successfully
2025-05-24 11:06:36 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:06:36 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:06:36 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-24 11:06:36 [INFO] Post-exam mode is enabled
2025-05-24 11:06:36 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:06:36 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:06:36 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:06:36 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:06:36 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:06:36 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:06:36 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:06:36 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:06:36 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:06:36 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:06:36 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:06:36 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:06:37 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:06:37 [INFO] Performing one-time device status check
2025-05-24 11:06:37 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:06:37 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:06:37 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:06:37 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:06:37 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:06:37 [INFO] One-time device status check complete
2025-05-24 11:06:44 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:06:45 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:06:45 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:06:45 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:06:45 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:06:46 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:06:46 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:06:46 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:06:46 [INFO] Global exam times - Start: 001200, End: 150000, Current: 110646
2025-05-24 11:06:46 [INFO] Global exam progress: 73.73498498498499% (Elapsed: 39286s, Duration: 53280s)
2025-05-24 11:06:46 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:06:46 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:06:59 [INFO] Loaded existing post-exam photo status: Verified
2025-05-24 11:07:03 [INFO] Post-exam verification status is Incomplete for candidate: 9350
2025-05-24 11:07:11 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:07:11 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:07:12 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:07:12 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:07:12 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:07:12 [INFO] No seat assignment found for 9356
2025-05-24 11:07:12 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9356
2025-05-24 11:07:12 [INFO] Entering post-exam verification mode for candidate: 9356
2025-05-24 11:07:21 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:07:21 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:07:21 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:07:21 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:07:21 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:07:22 [INFO] No seat assignment found for 9351
2025-05-24 11:07:22 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9351
2025-05-24 11:07:22 [INFO] Entering post-exam verification mode for candidate: 9351
2025-05-24 11:07:28 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:07:28 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:07:28 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:07:28 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:07:28 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:07:35 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:07:35 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:07:36 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:07:36 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:07:36 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:07:36 [INFO] No seat assignment found for 9353
2025-05-24 11:07:36 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9353
2025-05-24 11:07:36 [INFO] Entering post-exam verification mode for candidate: 9353
2025-05-24 11:07:42 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:07:42 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:07:43 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:07:43 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:07:43 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:07:43 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:07:43 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:07:43 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:07:43 [INFO] Global exam times - Start: 001200, End: 150000, Current: 110743
2025-05-24 11:07:44 [INFO] Global exam progress: 73.841966966966964% (Elapsed: 39343s, Duration: 53280s)
2025-05-24 11:07:44 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:07:44 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:07:50 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 11:07:50 [INFO] Unloaded avicap32.dll library
2025-05-24 11:07:56 [INFO] Error handler initialized
2025-05-24 11:07:56 [INFO] Read database path from config: db
2025-05-24 11:07:56 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:07:56 [INFO] Error handler initialized
2025-05-24 11:07:56 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:07:56 [INFO] PathManager initialized successfully
2025-05-24 11:07:56 [INFO] PathManager initialized successfully
2025-05-24 11:07:56 [INFO] Validating required files and directories
2025-05-24 11:07:56 [INFO] Validated directory: Database
2025-05-24 11:07:56 [INFO] Validated directory: Images
2025-05-24 11:07:56 [INFO] Validated directory: Logs
2025-05-24 11:07:56 [INFO] Validated directory: Temporary files
2025-05-24 11:07:56 [INFO] Validated directory: Candidate images
2025-05-24 11:07:56 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:07:56 [INFO] All required files and directories validated successfully
2025-05-24 11:07:56 [INFO] Initializing application components
2025-05-24 11:07:56 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:07:56 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:07:56 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:07:56 [INFO] Room cache loaded with 3 entries
2025-05-24 11:07:56 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:07:56 [INFO] Loaded 1 seat assignments
2025-05-24 11:07:56 [INFO] Database manager initialized successfully
2025-05-24 11:07:56 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:07:57 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:07:57 [DEBUG] IsObject check after creation: True
2025-05-24 11:07:57 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:07:58 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:07:58 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:07:58 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:07:58 [DEBUG] g_fingerprintManager class handle: 10342560
2025-05-24 11:07:58 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:07:59 [INFO] Webcam started successfully
2025-05-24 11:07:59 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:07:59 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:07:59 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-24 11:07:59 [INFO] Post-exam mode is enabled
2025-05-24 11:07:59 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:07:59 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:07:59 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:07:59 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:07:59 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:07:59 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:07:59 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:07:59 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:07:59 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:07:59 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:07:59 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:07:59 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:07:59 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:07:59 [INFO] Performing one-time device status check
2025-05-24 11:07:59 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:07:59 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:07:59 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:07:59 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:07:59 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:07:59 [INFO] One-time device status check complete
2025-05-24 11:08:03 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:08:03 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:08:03 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:08:03 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:08:03 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:08:04 [INFO] No seat assignment found for 9351
2025-05-24 11:08:04 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9351
2025-05-24 11:08:04 [INFO] Entering post-exam verification mode for candidate: 9351
2025-05-24 11:08:10 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:08:10 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:08:10 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:08:10 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:08:11 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:08:11 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:08:11 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:08:11 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:08:11 [INFO] Global exam times - Start: 001200, End: 150000, Current: 110811
2025-05-24 11:08:12 [INFO] Global exam progress: 73.89451951951952% (Elapsed: 39371s, Duration: 53280s)
2025-05-24 11:08:12 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:08:12 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:08:47 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:08:48 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:08:48 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:08:48 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:08:48 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:08:49 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:08:49 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:08:49 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:08:49 [INFO] Global exam times - Start: 001200, End: 150000, Current: 110849
2025-05-24 11:08:49 [INFO] Global exam progress: 73.965840840840841% (Elapsed: 39409s, Duration: 53280s)
2025-05-24 11:08:49 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:08:49 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:09:02 [INFO] Post-Exam Mode disabled by user
2025-05-24 11:09:06 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 11:09:06 [INFO] Unloaded avicap32.dll library
2025-05-24 11:09:11 [INFO] Error handler initialized
2025-05-24 11:09:11 [INFO] Read database path from config: db
2025-05-24 11:09:11 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:09:11 [INFO] Error handler initialized
2025-05-24 11:09:11 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:09:11 [INFO] PathManager initialized successfully
2025-05-24 11:09:11 [INFO] PathManager initialized successfully
2025-05-24 11:09:11 [INFO] Validating required files and directories
2025-05-24 11:09:11 [INFO] Validated directory: Database
2025-05-24 11:09:11 [INFO] Validated directory: Images
2025-05-24 11:09:11 [INFO] Validated directory: Logs
2025-05-24 11:09:11 [INFO] Validated directory: Temporary files
2025-05-24 11:09:11 [INFO] Validated directory: Candidate images
2025-05-24 11:09:11 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:09:11 [INFO] All required files and directories validated successfully
2025-05-24 11:09:11 [INFO] Initializing application components
2025-05-24 11:09:11 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:09:11 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:09:11 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:09:11 [INFO] Room cache loaded with 3 entries
2025-05-24 11:09:11 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:09:11 [INFO] Loaded 1 seat assignments
2025-05-24 11:09:11 [INFO] Database manager initialized successfully
2025-05-24 11:09:11 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:09:12 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:09:12 [DEBUG] IsObject check after creation: True
2025-05-24 11:09:12 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:09:13 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:09:13 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:09:13 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:09:13 [DEBUG] g_fingerprintManager class handle: 1341264
2025-05-24 11:09:13 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:09:14 [INFO] Webcam started successfully
2025-05-24 11:09:14 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:09:14 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:09:14 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-24 11:09:14 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:09:14 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:09:14 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:09:14 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:09:14 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:09:14 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:09:14 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:09:14 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:09:14 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:09:14 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:09:14 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:09:14 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:09:14 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:09:14 [INFO] Performing one-time device status check
2025-05-24 11:09:14 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:09:14 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:09:14 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:09:14 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:09:14 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:09:15 [INFO] One-time device status check complete
2025-05-24 11:09:19 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:09:20 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:09:20 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:09:20 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:09:20 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:09:21 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:09:30 [INFO] Post-Exam Mode enabled by user
2025-05-24 11:09:34 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:09:35 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:09:35 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:09:35 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:09:35 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:09:36 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:09:36 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:09:36 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:09:36 [INFO] Global exam times - Start: 001200, End: 150000, Current: 110936
2025-05-24 11:09:36 [INFO] Global exam progress: 74.054054054054049% (Elapsed: 39456s, Duration: 53280s)
2025-05-24 11:09:36 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:09:36 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:09:56 [INFO] Loaded existing post-exam photo status: Verified
2025-05-24 11:10:00 [INFO] Post-exam verification status is Incomplete for candidate: 9350
2025-05-24 11:10:05 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 11:10:06 [INFO] Unloaded avicap32.dll library
2025-05-24 11:10:09 [INFO] Error handler initialized
2025-05-24 11:10:09 [INFO] Read database path from config: db
2025-05-24 11:10:09 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:10:09 [INFO] Error handler initialized
2025-05-24 11:10:09 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:10:09 [INFO] PathManager initialized successfully
2025-05-24 11:10:09 [INFO] PathManager initialized successfully
2025-05-24 11:10:09 [INFO] Validating required files and directories
2025-05-24 11:10:09 [INFO] Validated directory: Database
2025-05-24 11:10:09 [INFO] Validated directory: Images
2025-05-24 11:10:09 [INFO] Validated directory: Logs
2025-05-24 11:10:09 [INFO] Validated directory: Temporary files
2025-05-24 11:10:09 [INFO] Validated directory: Candidate images
2025-05-24 11:10:09 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:10:09 [INFO] All required files and directories validated successfully
2025-05-24 11:10:09 [INFO] Initializing application components
2025-05-24 11:10:09 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:10:09 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:10:09 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:10:09 [INFO] Room cache loaded with 3 entries
2025-05-24 11:10:09 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:10:09 [INFO] Loaded 1 seat assignments
2025-05-24 11:10:09 [INFO] Database manager initialized successfully
2025-05-24 11:10:09 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:10:10 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:10:10 [DEBUG] IsObject check after creation: True
2025-05-24 11:10:10 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:10:11 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:10:11 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:10:11 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:10:11 [DEBUG] g_fingerprintManager class handle: 50991712
2025-05-24 11:10:11 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:10:12 [INFO] Webcam started successfully
2025-05-24 11:10:12 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:10:12 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:10:12 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-24 11:10:12 [INFO] Post-exam mode is enabled
2025-05-24 11:10:12 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:10:12 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:10:12 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:10:12 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:10:12 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:10:12 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:10:12 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:10:12 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:10:12 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:10:12 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:10:12 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:10:12 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:10:12 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:10:12 [INFO] Performing one-time device status check
2025-05-24 11:10:12 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:10:12 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:10:13 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:10:13 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:10:13 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:10:13 [INFO] One-time device status check complete
2025-05-24 11:10:17 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:10:17 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:10:18 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:10:18 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:10:18 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:10:18 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:10:18 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:10:18 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:10:18 [INFO] Global exam times - Start: 001200, End: 150000, Current: 111018
2025-05-24 11:10:18 [INFO] Global exam progress: 74.132882882882882% (Elapsed: 39498s, Duration: 53280s)
2025-05-24 11:10:18 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:10:18 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:11:41 [INFO] Loaded existing post-exam photo status: Verified
2025-05-24 11:12:02 [INFO] Post-exam verification status is Incomplete for candidate: 9350
2025-05-24 11:12:51 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:12:52 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:12:52 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:12:52 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:12:52 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:12:53 [INFO] No seat assignment found for 9351
2025-05-24 11:12:53 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9351
2025-05-24 11:12:53 [INFO] Entering post-exam verification mode for candidate: 9351
2025-05-24 11:13:00 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:13:00 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:13:01 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:13:01 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:13:01 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:13:01 [INFO] No seat assignment found for 9359
2025-05-24 11:13:01 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9359
2025-05-24 11:13:01 [INFO] Entering post-exam verification mode for candidate: 9359
2025-05-24 11:13:05 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:13:06 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:13:06 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:13:06 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:13:06 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:13:06 [INFO] No seat assignment found for 9351
2025-05-24 11:13:06 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9351
2025-05-24 11:13:06 [INFO] Entering post-exam verification mode for candidate: 9351
2025-05-24 11:13:33 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 11:13:34 [INFO] Unloaded avicap32.dll library
2025-05-24 11:13:38 [INFO] Error handler initialized
2025-05-24 11:13:38 [INFO] Read database path from config: db
2025-05-24 11:13:38 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:13:38 [INFO] Error handler initialized
2025-05-24 11:13:38 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:13:38 [INFO] PathManager initialized successfully
2025-05-24 11:13:38 [INFO] PathManager initialized successfully
2025-05-24 11:13:38 [INFO] Validating required files and directories
2025-05-24 11:13:38 [INFO] Validated directory: Database
2025-05-24 11:13:38 [INFO] Validated directory: Images
2025-05-24 11:13:38 [INFO] Validated directory: Logs
2025-05-24 11:13:38 [INFO] Validated directory: Temporary files
2025-05-24 11:13:38 [INFO] Validated directory: Candidate images
2025-05-24 11:13:38 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:13:38 [INFO] All required files and directories validated successfully
2025-05-24 11:13:38 [INFO] Initializing application components
2025-05-24 11:13:38 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:13:38 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:13:38 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:13:38 [INFO] Room cache loaded with 3 entries
2025-05-24 11:13:38 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:13:38 [INFO] Loaded 1 seat assignments
2025-05-24 11:13:38 [INFO] Database manager initialized successfully
2025-05-24 11:13:38 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:13:40 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:13:40 [DEBUG] IsObject check after creation: True
2025-05-24 11:13:40 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:13:40 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:13:40 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:13:40 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:13:40 [DEBUG] g_fingerprintManager class handle: 10066080
2025-05-24 11:13:40 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:13:41 [INFO] Webcam started successfully
2025-05-24 11:13:41 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:13:41 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:13:41 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-24 11:13:41 [INFO] Post-exam mode is enabled
2025-05-24 11:13:41 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:13:41 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:13:41 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:13:41 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:13:41 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:13:41 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:13:41 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:13:41 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:13:41 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:13:41 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:13:41 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:13:41 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:13:41 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:13:42 [INFO] Performing one-time device status check
2025-05-24 11:13:42 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:13:42 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:13:42 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:13:42 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:13:42 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:13:42 [INFO] One-time device status check complete
2025-05-24 11:13:52 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:13:53 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:13:53 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:13:53 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:13:53 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:13:54 [INFO] No seat assignment found for 9351
2025-05-24 11:13:54 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9351
2025-05-24 11:13:54 [INFO] Entering post-exam verification mode for candidate: 9351
2025-05-24 11:14:00 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:14:01 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:14:01 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:14:01 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:14:01 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:14:02 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:14:02 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:14:02 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:14:02 [INFO] Global exam times - Start: 001200, End: 150000, Current: 111402
2025-05-24 11:14:02 [INFO] Global exam progress: 74.553303303303309% (Elapsed: 39722s, Duration: 53280s)
2025-05-24 11:14:02 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:14:02 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:14:07 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 11:14:07 [INFO] Unloaded avicap32.dll library
2025-05-24 11:14:10 [INFO] Error handler initialized
2025-05-24 11:14:10 [INFO] Read database path from config: db
2025-05-24 11:14:10 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:14:10 [INFO] Error handler initialized
2025-05-24 11:14:10 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:14:10 [INFO] PathManager initialized successfully
2025-05-24 11:14:10 [INFO] PathManager initialized successfully
2025-05-24 11:14:10 [INFO] Validating required files and directories
2025-05-24 11:14:10 [INFO] Validated directory: Database
2025-05-24 11:14:10 [INFO] Validated directory: Images
2025-05-24 11:14:10 [INFO] Validated directory: Logs
2025-05-24 11:14:10 [INFO] Validated directory: Temporary files
2025-05-24 11:14:10 [INFO] Validated directory: Candidate images
2025-05-24 11:14:10 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:14:10 [INFO] All required files and directories validated successfully
2025-05-24 11:14:10 [INFO] Initializing application components
2025-05-24 11:14:10 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:14:10 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:14:10 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:14:10 [INFO] Room cache loaded with 3 entries
2025-05-24 11:14:10 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:14:10 [INFO] Loaded 1 seat assignments
2025-05-24 11:14:10 [INFO] Database manager initialized successfully
2025-05-24 11:14:10 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:14:12 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:14:12 [DEBUG] IsObject check after creation: True
2025-05-24 11:14:12 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:14:12 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:14:12 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:14:13 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:14:13 [DEBUG] g_fingerprintManager class handle: 50331152
2025-05-24 11:14:13 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:14:13 [INFO] Webcam started successfully
2025-05-24 11:14:13 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:14:13 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:14:13 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-24 11:14:13 [INFO] Post-exam mode is enabled
2025-05-24 11:14:13 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:14:13 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:14:13 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:14:13 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:14:13 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:14:13 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:14:13 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:14:13 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:14:14 [INFO] Performing one-time device status check
2025-05-24 11:14:14 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:14:14 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:14:14 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:14:14 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:14:14 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:14:14 [INFO] One-time device status check complete
2025-05-24 11:14:19 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:14:20 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:14:20 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:14:20 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:14:20 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:14:21 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:14:21 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:14:21 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:14:21 [INFO] Global exam times - Start: 001200, End: 150000, Current: 111421
2025-05-24 11:14:21 [INFO] Global exam progress: 74.588963963963963% (Elapsed: 39741s, Duration: 53280s)
2025-05-24 11:14:21 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:14:21 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:14:24 [INFO] Application exiting: Reload (Code: 0)
2025-05-24 11:14:24 [INFO] Unloaded avicap32.dll library
2025-05-24 11:14:24 [INFO] Error handler initialized
2025-05-24 11:14:24 [INFO] Read database path from config: db
2025-05-24 11:14:24 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:14:24 [INFO] Error handler initialized
2025-05-24 11:14:24 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:14:24 [INFO] PathManager initialized successfully
2025-05-24 11:14:24 [INFO] PathManager initialized successfully
2025-05-24 11:14:24 [INFO] Validating required files and directories
2025-05-24 11:14:24 [INFO] Validated directory: Database
2025-05-24 11:14:24 [INFO] Validated directory: Images
2025-05-24 11:14:24 [INFO] Validated directory: Logs
2025-05-24 11:14:24 [INFO] Validated directory: Temporary files
2025-05-24 11:14:24 [INFO] Validated directory: Candidate images
2025-05-24 11:14:24 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:14:24 [INFO] All required files and directories validated successfully
2025-05-24 11:14:24 [INFO] Initializing application components
2025-05-24 11:14:24 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:14:24 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:14:24 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:14:24 [INFO] Room cache loaded with 3 entries
2025-05-24 11:14:24 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:14:24 [INFO] Loaded 1 seat assignments
2025-05-24 11:14:24 [INFO] Database manager initialized successfully
2025-05-24 11:14:24 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:14:26 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:14:26 [DEBUG] IsObject check after creation: True
2025-05-24 11:14:26 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:14:27 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:14:27 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:14:27 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:14:27 [DEBUG] g_fingerprintManager class handle: 49809824
2025-05-24 11:14:27 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:14:27 [INFO] Webcam started successfully
2025-05-24 11:14:27 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:14:27 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:14:27 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-24 11:14:27 [INFO] Post-exam mode is enabled
2025-05-24 11:14:27 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:14:27 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:14:27 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:14:27 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:14:27 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:14:27 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:14:27 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:14:27 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:14:27 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:14:27 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:14:28 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:14:28 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:14:28 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:14:28 [INFO] Performing one-time device status check
2025-05-24 11:14:28 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:14:28 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:14:28 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:14:28 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:14:28 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:14:28 [INFO] One-time device status check complete
2025-05-24 11:14:31 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:14:32 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:14:32 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:14:32 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:14:32 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:14:33 [INFO] No seat assignment found for 9351
2025-05-24 11:14:33 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9351
2025-05-24 11:14:33 [INFO] Entering post-exam verification mode for candidate: 9351
2025-05-24 11:14:38 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:14:39 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:14:39 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:14:39 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:14:39 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:14:40 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:14:40 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:14:40 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:14:40 [INFO] Global exam times - Start: 001200, End: 150000, Current: 111440
2025-05-24 11:14:40 [INFO] Global exam progress: 74.62462462462463% (Elapsed: 39760s, Duration: 53280s)
2025-05-24 11:14:40 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:14:40 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:17:44 [INFO] Application exiting: Single (Code: 0)
2025-05-24 11:17:44 [INFO] Unloaded avicap32.dll library
2025-05-24 11:17:44 [INFO] Error handler initialized
2025-05-24 11:17:44 [INFO] Read database path from config: db
2025-05-24 11:17:44 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:17:44 [INFO] Error handler initialized
2025-05-24 11:17:44 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:17:44 [INFO] PathManager initialized successfully
2025-05-24 11:17:45 [INFO] PathManager initialized successfully
2025-05-24 11:17:45 [INFO] Validating required files and directories
2025-05-24 11:17:45 [INFO] Validated directory: Database
2025-05-24 11:17:45 [INFO] Validated directory: Images
2025-05-24 11:17:45 [INFO] Validated directory: Logs
2025-05-24 11:17:45 [INFO] Validated directory: Temporary files
2025-05-24 11:17:45 [INFO] Validated directory: Candidate images
2025-05-24 11:17:45 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:17:45 [INFO] All required files and directories validated successfully
2025-05-24 11:17:45 [INFO] Initializing application components
2025-05-24 11:17:45 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:17:45 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:17:45 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:17:45 [INFO] Room cache loaded with 3 entries
2025-05-24 11:17:45 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:17:45 [INFO] Loaded 1 seat assignments
2025-05-24 11:17:45 [INFO] Database manager initialized successfully
2025-05-24 11:17:45 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:17:46 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:17:46 [DEBUG] IsObject check after creation: True
2025-05-24 11:17:46 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:17:47 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:17:47 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:17:47 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:17:47 [DEBUG] g_fingerprintManager class handle: 10277488
2025-05-24 11:17:47 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:17:48 [INFO] Webcam started successfully
2025-05-24 11:17:48 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:17:48 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:17:48 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-24 11:17:48 [INFO] Post-exam mode is enabled
2025-05-24 11:17:48 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:17:48 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:17:48 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:17:48 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:17:48 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:17:48 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:17:48 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:17:48 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:17:48 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:17:48 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:17:48 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:17:48 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:17:48 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:17:48 [INFO] Performing one-time device status check
2025-05-24 11:17:48 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:17:48 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:17:49 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:17:49 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:17:49 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:17:49 [INFO] One-time device status check complete
2025-05-24 11:18:38 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:18:39 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:18:39 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:18:39 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:18:39 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:18:40 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:18:40 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:18:40 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:18:40 [INFO] Global exam times - Start: 001200, End: 150000, Current: 111840
2025-05-24 11:18:40 [INFO] Global exam progress: 75.075075075075077% (Elapsed: 40000s, Duration: 53280s)
2025-05-24 11:18:41 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:18:41 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:18:41 [INFO] Loaded existing post-exam photo status: Verified
2025-05-24 11:18:41 [INFO] Post-exam verification status is Incomplete for candidate: 9350
2025-05-24 11:18:48 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:18:49 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:18:49 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:18:49 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:18:49 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:18:49 [INFO] No seat assignment found for 9351
2025-05-24 11:18:49 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9351
2025-05-24 11:18:50 [INFO] Entering post-exam verification mode for candidate: 9351
2025-05-24 11:18:54 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:18:54 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:18:55 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:18:55 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:18:55 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:18:55 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:18:55 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:18:55 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:18:55 [INFO] Global exam times - Start: 001200, End: 150000, Current: 111855
2025-05-24 11:18:55 [INFO] Global exam progress: 75.103228228228218% (Elapsed: 40015s, Duration: 53280s)
2025-05-24 11:18:56 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:18:56 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:18:56 [INFO] Loaded existing post-exam photo status: Verified
2025-05-24 11:18:56 [INFO] Post-exam verification status is Incomplete for candidate: 9350
2025-05-24 11:19:01 [INFO] Application exiting: Single (Code: 0)
2025-05-24 11:19:01 [INFO] Unloaded avicap32.dll library
2025-05-24 11:19:01 [INFO] Error handler initialized
2025-05-24 11:19:01 [INFO] Read database path from config: db
2025-05-24 11:19:01 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:19:01 [INFO] Error handler initialized
2025-05-24 11:19:01 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:19:01 [INFO] PathManager initialized successfully
2025-05-24 11:19:01 [INFO] PathManager initialized successfully
2025-05-24 11:19:01 [INFO] Validating required files and directories
2025-05-24 11:19:01 [INFO] Validated directory: Database
2025-05-24 11:19:01 [INFO] Validated directory: Images
2025-05-24 11:19:01 [INFO] Validated directory: Logs
2025-05-24 11:19:01 [INFO] Validated directory: Temporary files
2025-05-24 11:19:01 [INFO] Validated directory: Candidate images
2025-05-24 11:19:01 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:19:01 [INFO] All required files and directories validated successfully
2025-05-24 11:19:02 [INFO] Initializing application components
2025-05-24 11:19:02 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:19:02 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:19:02 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:19:02 [INFO] Room cache loaded with 3 entries
2025-05-24 11:19:02 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:19:02 [INFO] Loaded 1 seat assignments
2025-05-24 11:19:02 [INFO] Database manager initialized successfully
2025-05-24 11:19:02 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:19:03 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:19:03 [DEBUG] IsObject check after creation: True
2025-05-24 11:19:03 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:19:04 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:19:04 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:19:04 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:19:04 [DEBUG] g_fingerprintManager class handle: 9044400
2025-05-24 11:19:04 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:19:05 [INFO] Webcam started successfully
2025-05-24 11:19:05 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:19:05 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:19:05 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-24 11:19:05 [INFO] Post-exam mode is enabled
2025-05-24 11:19:05 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:19:05 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:19:05 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:19:05 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:19:05 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:19:05 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:19:05 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:19:05 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:19:05 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:19:05 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:19:05 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:19:05 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:19:05 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:19:05 [INFO] Performing one-time device status check
2025-05-24 11:19:05 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:19:05 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:19:05 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:19:05 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:19:05 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:19:05 [INFO] One-time device status check complete
2025-05-24 11:19:15 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:19:15 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:19:16 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:19:16 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:19:16 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:19:16 [INFO] No seat assignment found for 9351
2025-05-24 11:19:16 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9351
2025-05-24 11:19:16 [INFO] Entering post-exam verification mode for candidate: 9351
2025-05-24 11:19:20 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:19:20 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:19:21 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:19:21 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:19:21 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:19:21 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:19:21 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:19:21 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:19:21 [INFO] Global exam times - Start: 001200, End: 150000, Current: 111921
2025-05-24 11:19:21 [INFO] Global exam progress: 75.152027027027017% (Elapsed: 40041s, Duration: 53280s)
2025-05-24 11:19:21 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:19:21 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:19:21 [INFO] Loaded existing post-exam photo status: Verified
2025-05-24 11:19:22 [INFO] Post-exam verification status is Incomplete for candidate: 9350
2025-05-24 11:19:27 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:19:28 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:19:28 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:19:28 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:19:28 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:19:32 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:19:32 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:19:33 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:19:33 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:19:33 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:19:33 [INFO] No seat assignment found for 9351
2025-05-24 11:19:33 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9351
2025-05-24 11:19:33 [INFO] Entering post-exam verification mode for candidate: 9351
2025-05-24 11:19:36 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:19:37 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:19:37 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:19:37 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:19:37 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:19:38 [INFO] No seat assignment found for 9353
2025-05-24 11:19:38 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9353
2025-05-24 11:19:38 [INFO] Entering post-exam verification mode for candidate: 9353
2025-05-24 11:19:48 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:19:49 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:19:49 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:19:49 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:19:49 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:19:55 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:19:55 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:19:56 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:19:56 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:19:56 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:19:57 [INFO] No seat assignment found for 9355
2025-05-24 11:19:57 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9355
2025-05-24 11:19:57 [INFO] Entering post-exam verification mode for candidate: 9355
2025-05-24 11:20:02 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:20:02 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:20:03 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:20:03 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:20:03 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:20:03 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:20:03 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:20:03 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:20:04 [INFO] Global exam times - Start: 001200, End: 150000, Current: 112004
2025-05-24 11:20:04 [INFO] Global exam progress: 75.232732732732728% (Elapsed: 40084s, Duration: 53280s)
2025-05-24 11:20:04 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:20:04 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:20:04 [INFO] Loaded existing post-exam photo status: Verified
2025-05-24 11:20:04 [INFO] Post-exam verification status is Incomplete for candidate: 9350
2025-05-24 11:20:56 [INFO] Post-Exam Mode disabled by user
2025-05-24 11:21:00 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:21:00 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:21:01 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:21:01 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:21:01 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:21:01 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:21:47 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:21:47 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:21:47 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:21:47 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:21:47 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:21:48 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:21:54 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:21:55 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:21:55 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:21:55 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:21:55 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:21:56 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:22:10 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:22:10 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:22:11 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:22:11 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:22:11 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:22:11 [INFO] No seat assignment found for 9351
2025-05-24 11:22:18 [INFO] Assigning seat for candidate: 9351 (Sanjay Kumar)
2025-05-24 11:22:18 [INFO] No seat assignment found for 9351
2025-05-24 11:22:18 [DEBUG] Room Cache: 3 entries
2025-05-24 11:22:18 [DEBUG] Hardware Cache: 10 entries
2025-05-24 11:22:18 [DEBUG] Seat Assignment Cache: 1 entries
2025-05-24 11:22:18 [INFO] Seat assigned successfully for 9351: F1-R1-S7 (Special Needs allocation)
2025-05-24 11:22:28 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:22:28 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:22:28 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:22:28 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:22:28 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:22:29 [INFO] Found seat assignment in cache for 9351: F1-R1-S7
2025-05-24 11:22:41 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:22:41 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:22:42 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:22:42 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:22:42 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:22:42 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:23:08 [INFO] Post-Exam Mode enabled by user
2025-05-24 11:24:05 [INFO] Post-Exam Mode toggle cancelled by user
2025-05-24 11:24:08 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:24:08 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:24:09 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:24:09 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:24:09 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:24:09 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:24:09 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:24:09 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:24:10 [INFO] Global exam times - Start: 001200, End: 150000, Current: 112410
2025-05-24 11:24:10 [INFO] Global exam progress: 75.694444444444443% (Elapsed: 40330s, Duration: 53280s)
2025-05-24 11:24:10 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:24:10 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:24:10 [INFO] Loaded existing post-exam photo status: Verified
2025-05-24 11:24:10 [INFO] Post-exam verification status is Incomplete for candidate: 9350
2025-05-24 11:24:25 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:24:26 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:24:26 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:24:26 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:24:26 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:24:27 [INFO] Found seat assignment in cache for 9351: F1-R1-S7
2025-05-24 11:24:27 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:24:27 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:24:27 [INFO] Global exam times - Start: 001200, End: 150000, Current: 112427
2025-05-24 11:24:27 [INFO] Global exam progress: 75.726351351351354% (Elapsed: 40347s, Duration: 53280s)
2025-05-24 11:24:27 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:24:27 [INFO] Entering post-exam verification mode for candidate: 9351
2025-05-24 11:24:27 [INFO] Special candidate detected in post-exam mode: 9351
2025-05-24 11:24:38 [INFO] Post-exam verification status is Incomplete for candidate: 9351
2025-05-24 11:24:56 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:24:56 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:24:57 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:24:57 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:24:57 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:24:57 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:24:57 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:24:57 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:24:57 [INFO] Global exam times - Start: 001200, End: 150000, Current: 112457
2025-05-24 11:24:57 [INFO] Global exam progress: 75.782657657657666% (Elapsed: 40377s, Duration: 53280s)
2025-05-24 11:24:57 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:24:57 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:24:57 [INFO] Loaded existing post-exam photo status: Verified
2025-05-24 11:24:57 [INFO] Post-exam verification status is Incomplete for candidate: 9350
2025-05-24 11:25:04 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:25:05 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:25:05 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:25:05 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:25:05 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:25:05 [INFO] Found seat assignment in cache for 9351: F1-R1-S7
2025-05-24 11:25:05 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:25:05 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:25:06 [INFO] Global exam times - Start: 001200, End: 150000, Current: 112506
2025-05-24 11:25:06 [INFO] Global exam progress: 75.799549549549553% (Elapsed: 40386s, Duration: 53280s)
2025-05-24 11:25:06 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:25:06 [INFO] Entering post-exam verification mode for candidate: 9351
2025-05-24 11:25:06 [INFO] Special candidate detected in post-exam mode: 9351
2025-05-24 11:25:15 [INFO] Post-exam verification status is Incomplete for candidate: 9351
2025-05-24 11:28:35 [INFO] Post-Exam Mode disabled by user
2025-05-24 11:32:06 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 11:32:07 [INFO] Unloaded avicap32.dll library
2025-05-24 11:32:09 [INFO] Error handler initialized
2025-05-24 11:32:09 [INFO] Read database path from config: db
2025-05-24 11:32:09 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:32:09 [INFO] Error handler initialized
2025-05-24 11:32:10 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:32:10 [INFO] PathManager initialized successfully
2025-05-24 11:32:10 [INFO] PathManager initialized successfully
2025-05-24 11:32:10 [INFO] Validating required files and directories
2025-05-24 11:32:10 [INFO] Validated directory: Database
2025-05-24 11:32:10 [INFO] Validated directory: Images
2025-05-24 11:32:10 [INFO] Validated directory: Logs
2025-05-24 11:32:10 [INFO] Validated directory: Temporary files
2025-05-24 11:32:10 [INFO] Validated directory: Candidate images
2025-05-24 11:32:10 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:32:10 [INFO] All required files and directories validated successfully
2025-05-24 11:32:10 [INFO] Initializing application components
2025-05-24 11:32:10 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:32:10 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:32:10 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:32:10 [INFO] Room cache loaded with 3 entries
2025-05-24 11:32:10 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:32:10 [INFO] Loaded 2 seat assignments
2025-05-24 11:32:10 [INFO] Database manager initialized successfully
2025-05-24 11:32:10 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:32:11 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:32:11 [DEBUG] IsObject check after creation: True
2025-05-24 11:32:11 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:32:12 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:32:12 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:32:12 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:32:12 [DEBUG] g_fingerprintManager class handle: 854672
2025-05-24 11:32:12 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:32:13 [INFO] Webcam started successfully
2025-05-24 11:32:13 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:32:13 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:32:13 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-24 11:32:13 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:32:13 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:32:13 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:32:13 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:32:13 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:32:13 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:32:13 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:32:13 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:32:13 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:32:13 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:32:13 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:32:13 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:32:13 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:32:13 [INFO] Performing one-time device status check
2025-05-24 11:32:13 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:32:13 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:32:13 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:32:13 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:32:13 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:32:13 [INFO] One-time device status check complete
2025-05-24 11:32:19 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 11:32:19 [INFO] Unloaded avicap32.dll library
2025-05-24 11:33:22 [INFO] Error handler initialized
2025-05-24 11:33:22 [INFO] Read database path from config: db
2025-05-24 11:33:22 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:33:22 [INFO] Error handler initialized
2025-05-24 11:33:22 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:33:22 [INFO] PathManager initialized successfully
2025-05-24 11:33:22 [INFO] PathManager initialized successfully
2025-05-24 11:33:22 [INFO] Validating required files and directories
2025-05-24 11:33:22 [INFO] Validated directory: Database
2025-05-24 11:33:22 [INFO] Validated directory: Images
2025-05-24 11:33:22 [INFO] Validated directory: Logs
2025-05-24 11:33:22 [INFO] Validated directory: Temporary files
2025-05-24 11:33:22 [INFO] Validated directory: Candidate images
2025-05-24 11:33:22 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:33:22 [INFO] All required files and directories validated successfully
2025-05-24 11:33:22 [INFO] Initializing application components
2025-05-24 11:33:22 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:33:22 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:33:22 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:33:22 [INFO] Room cache loaded with 3 entries
2025-05-24 11:33:22 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:33:22 [INFO] Loaded 2 seat assignments
2025-05-24 11:33:22 [INFO] Database manager initialized successfully
2025-05-24 11:33:22 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:33:24 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:33:24 [DEBUG] IsObject check after creation: True
2025-05-24 11:33:24 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:33:25 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:33:25 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:33:25 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:33:25 [DEBUG] g_fingerprintManager class handle: 51712528
2025-05-24 11:33:25 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:33:26 [INFO] Webcam started successfully
2025-05-24 11:33:26 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:33:26 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:33:26 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-24 11:33:26 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:33:26 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:33:26 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:33:26 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:33:26 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:33:26 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:33:26 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:33:26 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:33:26 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:33:26 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:33:26 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:33:26 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:33:26 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:33:27 [INFO] Performing one-time device status check
2025-05-24 11:33:27 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:33:27 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:33:27 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:33:27 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:33:27 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:33:27 [INFO] One-time device status check complete
2025-05-24 11:33:33 [INFO] Post-Exam Mode enabled by user
2025-05-24 11:33:37 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 11:33:37 [INFO] Unloaded avicap32.dll library
2025-05-24 11:33:40 [INFO] Error handler initialized
2025-05-24 11:33:40 [INFO] Read database path from config: db
2025-05-24 11:33:40 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:33:40 [INFO] Error handler initialized
2025-05-24 11:33:40 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:33:40 [INFO] PathManager initialized successfully
2025-05-24 11:33:40 [INFO] PathManager initialized successfully
2025-05-24 11:33:40 [INFO] Validating required files and directories
2025-05-24 11:33:40 [INFO] Validated directory: Database
2025-05-24 11:33:40 [INFO] Validated directory: Images
2025-05-24 11:33:40 [INFO] Validated directory: Logs
2025-05-24 11:33:40 [INFO] Validated directory: Temporary files
2025-05-24 11:33:40 [INFO] Validated directory: Candidate images
2025-05-24 11:33:40 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:33:40 [INFO] All required files and directories validated successfully
2025-05-24 11:33:40 [INFO] Initializing application components
2025-05-24 11:33:40 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:33:40 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:33:40 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:33:40 [INFO] Room cache loaded with 3 entries
2025-05-24 11:33:40 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:33:40 [INFO] Loaded 2 seat assignments
2025-05-24 11:33:40 [INFO] Database manager initialized successfully
2025-05-24 11:33:40 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:33:42 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:33:42 [DEBUG] IsObject check after creation: True
2025-05-24 11:33:42 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:33:42 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:33:42 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:33:42 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:33:42 [DEBUG] g_fingerprintManager class handle: 10732000
2025-05-24 11:33:42 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:33:43 [INFO] Webcam started successfully
2025-05-24 11:33:43 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:33:43 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:33:43 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-24 11:33:43 [INFO] Post-exam mode is enabled
2025-05-24 11:33:43 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:33:43 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:33:43 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:33:43 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:33:43 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:33:43 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:33:43 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:33:43 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:33:43 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:33:43 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:33:43 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:33:44 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:33:44 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:33:44 [INFO] Performing one-time device status check
2025-05-24 11:33:44 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:33:44 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:33:44 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:33:44 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:33:44 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:33:44 [INFO] One-time device status check complete
2025-05-24 11:36:12 [INFO] Application exiting: Single (Code: 0)
2025-05-24 11:36:13 [INFO] Unloaded avicap32.dll library
2025-05-24 11:36:13 [INFO] Error handler initialized
2025-05-24 11:36:13 [INFO] Read database path from config: db
2025-05-24 11:36:13 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:36:13 [INFO] Error handler initialized
2025-05-24 11:36:13 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:36:13 [INFO] PathManager initialized successfully
2025-05-24 11:36:13 [INFO] PathManager initialized successfully
2025-05-24 11:36:13 [INFO] Validating required files and directories
2025-05-24 11:36:13 [INFO] Validated directory: Database
2025-05-24 11:36:13 [INFO] Validated directory: Images
2025-05-24 11:36:13 [INFO] Validated directory: Logs
2025-05-24 11:36:13 [INFO] Validated directory: Temporary files
2025-05-24 11:36:13 [INFO] Validated directory: Candidate images
2025-05-24 11:36:13 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:36:13 [INFO] All required files and directories validated successfully
2025-05-24 11:36:13 [INFO] Initializing application components
2025-05-24 11:36:13 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:36:13 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:36:13 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:36:13 [INFO] Room cache loaded with 3 entries
2025-05-24 11:36:13 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:36:13 [INFO] Loaded 2 seat assignments
2025-05-24 11:36:13 [INFO] Database manager initialized successfully
2025-05-24 11:36:13 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:36:15 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:36:15 [DEBUG] IsObject check after creation: True
2025-05-24 11:36:15 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:36:16 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:36:16 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:36:16 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:36:16 [DEBUG] g_fingerprintManager class handle: 46406512
2025-05-24 11:36:16 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:36:16 [INFO] Webcam started successfully
2025-05-24 11:36:16 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:36:16 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:36:16 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-24 11:36:16 [INFO] Post-exam mode is enabled
2025-05-24 11:36:16 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:36:16 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:36:16 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:36:16 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:36:16 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:36:16 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:36:16 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:36:16 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:36:16 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:36:16 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:36:17 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:36:17 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:36:17 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:36:17 [INFO] Performing one-time device status check
2025-05-24 11:36:17 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:36:17 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:36:17 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:36:17 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:36:17 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:36:17 [INFO] One-time device status check complete
2025-05-24 11:36:31 [INFO] Post-Exam Mode disabled by user
2025-05-24 11:37:01 [INFO] Application exiting: Single (Code: 0)
2025-05-24 11:37:02 [INFO] Unloaded avicap32.dll library
2025-05-24 11:37:02 [INFO] Error handler initialized
2025-05-24 11:37:02 [INFO] Read database path from config: db
2025-05-24 11:37:02 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:37:02 [INFO] Error handler initialized
2025-05-24 11:37:02 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:37:02 [INFO] PathManager initialized successfully
2025-05-24 11:37:02 [INFO] PathManager initialized successfully
2025-05-24 11:37:02 [INFO] Validating required files and directories
2025-05-24 11:37:02 [INFO] Validated directory: Database
2025-05-24 11:37:02 [INFO] Validated directory: Images
2025-05-24 11:37:02 [INFO] Validated directory: Logs
2025-05-24 11:37:02 [INFO] Validated directory: Temporary files
2025-05-24 11:37:02 [INFO] Validated directory: Candidate images
2025-05-24 11:37:02 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:37:02 [INFO] All required files and directories validated successfully
2025-05-24 11:37:02 [INFO] Initializing application components
2025-05-24 11:37:02 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:37:02 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:37:02 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:37:02 [INFO] Room cache loaded with 3 entries
2025-05-24 11:37:02 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:37:02 [INFO] Loaded 2 seat assignments
2025-05-24 11:37:02 [INFO] Database manager initialized successfully
2025-05-24 11:37:02 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:37:04 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:37:04 [DEBUG] IsObject check after creation: True
2025-05-24 11:37:04 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:37:05 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:37:05 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:37:05 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:37:05 [DEBUG] g_fingerprintManager class handle: 1602272
2025-05-24 11:37:05 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:37:05 [INFO] Webcam started successfully
2025-05-24 11:37:05 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:37:06 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:37:06 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-24 11:37:06 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:37:06 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:37:06 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:37:06 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:37:06 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:37:06 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:37:06 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:37:06 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:37:06 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:37:06 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:37:06 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:37:06 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:37:06 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:37:06 [INFO] Performing one-time device status check
2025-05-24 11:37:06 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:37:06 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:37:06 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:37:06 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:37:06 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:37:06 [INFO] One-time device status check complete
2025-05-24 11:37:32 [INFO] Post-Exam Mode enabled by user
2025-05-24 11:38:17 [INFO] Application exiting: Single (Code: 0)
2025-05-24 11:38:18 [INFO] Unloaded avicap32.dll library
2025-05-24 11:38:18 [INFO] Error handler initialized
2025-05-24 11:38:18 [INFO] Read database path from config: db
2025-05-24 11:38:18 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:38:18 [INFO] Error handler initialized
2025-05-24 11:38:18 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:38:18 [INFO] PathManager initialized successfully
2025-05-24 11:38:18 [INFO] PathManager initialized successfully
2025-05-24 11:38:18 [INFO] Validating required files and directories
2025-05-24 11:38:18 [INFO] Validated directory: Database
2025-05-24 11:38:18 [INFO] Validated directory: Images
2025-05-24 11:38:18 [INFO] Validated directory: Logs
2025-05-24 11:38:18 [INFO] Validated directory: Temporary files
2025-05-24 11:38:18 [INFO] Validated directory: Candidate images
2025-05-24 11:38:18 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:38:18 [INFO] All required files and directories validated successfully
2025-05-24 11:38:18 [INFO] Initializing application components
2025-05-24 11:38:18 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:38:18 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:38:18 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:38:18 [INFO] Room cache loaded with 3 entries
2025-05-24 11:38:18 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:38:18 [INFO] Loaded 2 seat assignments
2025-05-24 11:38:18 [INFO] Database manager initialized successfully
2025-05-24 11:38:18 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:38:20 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:38:20 [DEBUG] IsObject check after creation: True
2025-05-24 11:38:20 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:38:21 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:38:21 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:38:21 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:38:21 [DEBUG] g_fingerprintManager class handle: 9743696
2025-05-24 11:38:21 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:38:22 [INFO] Webcam started successfully
2025-05-24 11:38:22 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:38:22 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:38:22 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-24 11:38:22 [INFO] Post-exam mode is enabled
2025-05-24 11:38:22 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:38:22 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:38:22 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:38:22 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:38:22 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:38:22 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:38:22 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:38:22 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:38:22 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:38:22 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:38:22 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:38:22 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:38:22 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:38:22 [INFO] Performing one-time device status check
2025-05-24 11:38:22 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:38:22 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:38:22 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:38:22 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:38:22 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:38:22 [INFO] One-time device status check complete
2025-05-24 11:38:30 [INFO] Post-Exam Mode disabled by user
2025-05-24 11:38:50 [INFO] Application exiting: Single (Code: 0)
2025-05-24 11:38:51 [INFO] Unloaded avicap32.dll library
2025-05-24 11:38:51 [INFO] Error handler initialized
2025-05-24 11:38:51 [INFO] Read database path from config: db
2025-05-24 11:38:51 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:38:51 [INFO] Error handler initialized
2025-05-24 11:38:51 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:38:51 [INFO] PathManager initialized successfully
2025-05-24 11:38:51 [INFO] PathManager initialized successfully
2025-05-24 11:38:51 [INFO] Validating required files and directories
2025-05-24 11:38:51 [INFO] Validated directory: Database
2025-05-24 11:38:51 [INFO] Validated directory: Images
2025-05-24 11:38:51 [INFO] Validated directory: Logs
2025-05-24 11:38:51 [INFO] Validated directory: Temporary files
2025-05-24 11:38:51 [INFO] Validated directory: Candidate images
2025-05-24 11:38:51 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:38:51 [INFO] All required files and directories validated successfully
2025-05-24 11:38:51 [INFO] Initializing application components
2025-05-24 11:38:51 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:38:51 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:38:51 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:38:51 [INFO] Room cache loaded with 3 entries
2025-05-24 11:38:51 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:38:51 [INFO] Loaded 2 seat assignments
2025-05-24 11:38:51 [INFO] Database manager initialized successfully
2025-05-24 11:38:51 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:38:53 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:38:53 [DEBUG] IsObject check after creation: True
2025-05-24 11:38:53 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:38:53 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:38:53 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:38:53 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:38:53 [DEBUG] g_fingerprintManager class handle: 9482304
2025-05-24 11:38:53 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:38:54 [INFO] Webcam started successfully
2025-05-24 11:38:54 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:38:54 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:38:54 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-24 11:38:54 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:38:54 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:38:54 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:38:54 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:38:54 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:38:54 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:38:54 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:38:54 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:38:54 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:38:54 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:38:54 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:38:54 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:38:54 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:38:55 [INFO] Performing one-time device status check
2025-05-24 11:38:55 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:38:55 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:38:55 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:38:55 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:38:55 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:38:55 [INFO] One-time device status check complete
2025-05-24 11:39:02 [INFO] Post-Exam Mode enabled by user
2025-05-24 11:39:09 [INFO] Post-Exam Mode disabled by user
2025-05-24 11:39:30 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:39:31 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:39:31 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:39:31 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:39:31 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:39:32 [INFO] Found seat assignment in cache for 9351: F1-R1-S7
2025-05-24 11:43:47 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 11:43:47 [INFO] Unloaded avicap32.dll library
